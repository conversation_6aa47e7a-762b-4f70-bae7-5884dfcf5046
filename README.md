# T0T Trading System

T0T Trading System是一个基于Python的A股自动交易系统，专注于仓位选择、交易策略和风险管理。系统采用判定式交易策略，不预测市场未来涨跌，而是根据当前市场状态做出决策。

## 系统特点

- **判定式交易**：不预测市场涨跌，根据当前市场状态做出决策
- **多周期分析**：结合月线、周线、日线和分钟线进行多周期联合分析
- **技术指标应用**：使用均线、MACD、KDJ等技术指标进行分析
- **顶底背离识别**：识别价格与指标之间的背离，捕捉市场转折点
- **T+0交易**：实现日内高抛低吸，提高资金利用效率
- **风险管理**：包含完善的止损和交易修正机制

## 系统架构

系统由以下主要模块组成：

1. **数据获取模块**：获取市场数据和个股数据
2. **技术指标模块**：计算各种技术指标和识别背离
3. **仓位管理模块**：根据市场状态调整整体仓位
4. **T0交易模块**：实现日内高低点交易
5. **风险管理模块**：处理错误交易和执行止损
6. **回测分析模块**：评估策略性能

## 安装依赖

```bash
pip install pandas numpy matplotlib seaborn tqdm
# 可选数据源
pip install tushare akshare baostock
```

## 使用方法

### 回测模式

```bash
python -m t0t_trading_system.main --mode backtest --start_date 2020-01-01 --end_date 2023-12-31 --plot
```

参数说明：
- `--mode`：运行模式，可选 `backtest` 或 `live`
- `--start_date`：回测开始日期
- `--end_date`：回测结束日期
- `--index_symbol`：指数代码，默认为上证指数 (000001.SH)
- `--stock_symbol`：股票代码，如不指定则使用指数数据
- `--data_source`：数据源，可选 `tushare`、`akshare`、`baostock` 或 `mock`
- `--token`：数据源API token
- `--plot`：是否绘制回测结果

### 实时交易模式（暂未实现）

```bash
python -m t0t_trading_system.main --mode live
```

## 策略说明

### 仓位管理策略

系统根据月线级别的市场状态确定整体仓位范围：
- 市场底部：重仓（最高85%）
- 市场顶部：轻仓（最低15%）
- 其他时间：半仓（约50%）

具体判断标准：
1. **底部确认**：
   - 月收盘价跌破144周期月均线
   - 月线MACD、KDJ指标出现底背离
   - 价格创下近5年新低

2. **顶部确认**：
   - 月K线最高价创下最近5年新高
   - 月线MACD、KDJ指标出现顶背离
   - 月收盘价在5月均线上方

### T0交易策略

系统通过以下方法识别日内高低点：
1. **背离识别**：
   - 使用分钟线MACD、KDJ的顶底背离点识别局部高低点
   - 要求MACD、KDJ指标同时出现背离

2. **斐波那契水平**：
   - 结合斐波那契回调水平增强信号可靠性
   - 当价格在斐波那契水平附近出现背离时，信号强度加倍

3. **ATR波幅**：
   - 使用ATR指标预设当天的高低点位
   - 当价格在预设位置附近出现指标背离时作买卖决策

### 风险控制策略

系统包含完善的风险控制机制：
1. **交易失败判定**：
   - 价格出现相反方向的运动
   - 依据的交易周期背离信号消失
   - 上一层周期的指标未出现背离

2. **止损策略**：
   - 当满足交易失败条件时执行止损
   - 结合多时间框架分析，避免频繁止损

## 测试

运行单元测试：

```bash
python -m unittest discover -s t0t_trading_system/tests
```

## 许可证

MIT

## 免责声明

本系统仅供学习和研究使用，不构成投资建议。使用本系统进行实盘交易的风险由用户自行承担。
