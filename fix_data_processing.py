#!/usr/bin/env python3
"""
修复数据处理脚本 - 正确解析extracted原始数据
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import re

def parse_extracted_csv(file_path):
    """
    解析extracted目录中格式有问题的CSV文件

    原始数据格式分析：
    Time,Open,High,Low,Close,Volume,Amount,TVolume,TAmount,
    2023/1/3 9:31:00,3087.5103087.510,3078.7703083.870,1002458.00010024588288.000,1002458.00010024588288.000,

    问题：
    - 第2列：Open+High合并 (3087.5103087.510)
    - 第3列：Low+Close合并 (3078.7703083.870)
    - 第4列：Volume+Amount合并 (1002458.00010024588288.000)
    - 第5列：TVolume+TAmount合并 (1002458.00010024588288.000)
    """
    print(f"正在处理文件: {file_path}")

    try:
        # 读取原始文件
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # 解析数据
        parsed_data = []

        for i, line in enumerate(lines):
            if i == 0:  # 跳过标题行
                continue

            line = line.strip()
            if not line or line.endswith(','):
                line = line.rstrip(',')

            parts = line.split(',')
            if len(parts) < 4:
                continue

            try:
                # 解析时间
                time_str = parts[0]

                # 解析Open+High (第2列)
                open_high_str = parts[1]
                # 格式: 3087.5103087.510 -> 3087.510 和 3087.510
                if '.' in open_high_str:
                    # 找到所有数字和小数点的位置
                    digits = re.findall(r'\d+\.\d+', open_high_str)
                    if len(digits) >= 2:
                        open_price = float(digits[0])
                        high_price = float(digits[1])
                    elif len(digits) == 1:
                        # 如果只找到一个数字，可能是格式问题，尝试手动分割
                        num_str = digits[0]
                        if len(open_high_str) > len(num_str):
                            # 尝试从中间分割
                            mid = len(open_high_str) // 2
                            try:
                                open_price = float(open_high_str[:mid])
                                high_price = float(open_high_str[mid:])
                            except:
                                open_price = high_price = float(num_str)
                        else:
                            open_price = high_price = float(num_str)
                    else:
                        continue
                else:
                    continue

                # 解析Low+Close (第3列)
                low_close_str = parts[2]
                digits = re.findall(r'\d+\.\d+', low_close_str)
                if len(digits) >= 2:
                    low_price = float(digits[0])
                    close_price = float(digits[1])
                elif len(digits) == 1:
                    num_str = digits[0]
                    if len(low_close_str) > len(num_str):
                        mid = len(low_close_str) // 2
                        try:
                            low_price = float(low_close_str[:mid])
                            close_price = float(low_close_str[mid:])
                        except:
                            low_price = close_price = float(num_str)
                    else:
                        low_price = close_price = float(num_str)
                else:
                    continue

                # 解析Volume+Amount (第4列)
                vol_amt_str = parts[3]
                digits = re.findall(r'\d+\.\d+', vol_amt_str)
                if len(digits) >= 2:
                    volume = float(digits[0])
                    amount = float(digits[1])
                elif len(digits) == 1:
                    # 对于Volume+Amount，通常Volume较小，Amount较大
                    # 尝试智能分割
                    full_str = vol_amt_str
                    if '000' in full_str:
                        # 寻找连续的000作为分割点
                        parts_split = full_str.split('000')
                        if len(parts_split) >= 2:
                            try:
                                volume = float(parts_split[0] + '000')
                                amount = float(''.join(parts_split[1:]))
                            except:
                                volume = amount = float(digits[0])
                        else:
                            volume = amount = float(digits[0])
                    else:
                        volume = amount = float(digits[0])
                else:
                    volume = amount = 0.0

                # 解析TVolume+TAmount (第5列，如果存在)
                if len(parts) > 4:
                    tvol_tamt_str = parts[4]
                    digits = re.findall(r'\d+\.\d+', tvol_tamt_str)
                    if len(digits) >= 2:
                        tvolume = float(digits[0])
                        tamount = float(digits[1])
                    elif len(digits) == 1:
                        if '000' in tvol_tamt_str:
                            parts_split = tvol_tamt_str.split('000')
                            if len(parts_split) >= 2:
                                try:
                                    tvolume = float(parts_split[0] + '000')
                                    tamount = float(''.join(parts_split[1:]))
                                except:
                                    tvolume = tamount = float(digits[0])
                            else:
                                tvolume = tamount = float(digits[0])
                        else:
                            tvolume = tamount = float(digits[0])
                    else:
                        tvolume = tamount = 0.0
                else:
                    tvolume = volume
                    tamount = amount

                # 添加解析后的数据
                parsed_data.append([
                    time_str, open_price, high_price, low_price, close_price,
                    volume, amount, tvolume, tamount
                ])

            except (ValueError, IndexError) as e:
                print(f"解析第{i+1}行时出错: {line[:50]}... 错误: {e}")
                continue

        if not parsed_data:
            print("没有成功解析任何数据")
            return None

        # 创建DataFrame
        header = ['Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'Amount', 'TVolume', 'TAmount']
        df = pd.DataFrame(parsed_data, columns=header)

        # 转换时间格式
        df['datetime'] = pd.to_datetime(df['Time'], format='%Y/%m/%d %H:%M:%S')
        df = df.drop('Time', axis=1)

        # 重新排列列
        df = df[['datetime', 'Open', 'High', 'Low', 'Close', 'Volume', 'Amount', 'TVolume', 'TAmount']]
        df.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume', 'amount', 'tvolume', 'tamount']

        print(f"成功解析 {len(df)} 行数据")
        return df

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return None

def process_all_extracted_data():
    """处理所有extracted数据"""
    extracted_base = "/home/<USER>/Work/LLM/data/extracted"
    output_dir = "data/fixed_processed"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 处理SH000001数据
    all_data = []
    
    for year in ['2023', '2024']:
        year_path = os.path.join(extracted_base, year)
        if not os.path.exists(year_path):
            continue
            
        for month in os.listdir(year_path):
            month_path = os.path.join(year_path, month)
            if not os.path.isdir(month_path):
                continue
                
            for day in os.listdir(month_path):
                day_path = os.path.join(month_path, day)
                if not os.path.isdir(day_path):
                    continue
                    
                sh000001_file = os.path.join(day_path, "SH000001.csv")
                if os.path.exists(sh000001_file):
                    df = parse_extracted_csv(sh000001_file)
                    if df is not None and len(df) > 0:
                        all_data.append(df)
    
    if all_data:
        # 合并所有数据
        combined_df = pd.concat(all_data, ignore_index=True)
        combined_df = combined_df.sort_values('datetime')
        
        # 保存到文件
        output_file = os.path.join(output_dir, "SH000001.csv")
        combined_df.to_csv(output_file, index=False)
        
        print(f"\n总共处理了 {len(combined_df)} 行数据")
        print(f"数据时间范围: {combined_df['datetime'].min()} 到 {combined_df['datetime'].max()}")
        print(f"保存到: {output_file}")
        
        return combined_df
    else:
        print("没有找到有效数据")
        return None

def test_single_file():
    """测试单个文件的解析"""
    test_file = "/home/<USER>/Work/LLM/data/extracted/2023/01/2023-01-03/SH000001.csv"
    print(f"测试解析单个文件: {test_file}")

    df = parse_extracted_csv(test_file)
    if df is not None:
        print("\n解析结果:")
        print(df.head(10))
        print(f"\n数据统计:")
        print(f"总行数: {len(df)}")
        print(f"时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
        return True
    return False

if __name__ == "__main__":
    print("开始修复数据处理...")

    # 先测试单个文件
    if test_single_file():
        print("\n单文件测试成功，开始处理所有数据...")
        df = process_all_extracted_data()

        if df is not None:
            print("\n数据样本:")
            print(df.head(10))
            print(f"\n数据统计:")
            print(f"总行数: {len(df)}")
            print(f"日期范围: {df['datetime'].dt.date.min()} 到 {df['datetime'].dt.date.max()}")
            print(f"每日平均数据点: {len(df) / df['datetime'].dt.date.nunique():.1f}")
    else:
        print("单文件测试失败，请检查解析逻辑")
