#!/usr/bin/env python3
"""
测试其他标的的T0策略回测
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from run_t0_backtest import load_config, run_improved_t0_backtest
import shutil

def test_symbol_backtest(symbol):
    """测试指定标的的回测"""
    print(f"\n{'='*60}")
    print(f"测试 {symbol} 的T0策略回测")
    print(f"{'='*60}")
    
    # 检查数据文件是否存在
    data_file = f"data/fixed_processed/{symbol}.csv"
    if not os.path.exists(data_file):
        print(f"❌ {symbol} 数据文件不存在: {data_file}")
        return False
    
    # 备份当前的run_t0_backtest.py配置
    backup_file = "run_t0_backtest_backup.py"
    if os.path.exists("run_t0_backtest.py"):
        shutil.copy("run_t0_backtest.py", backup_file)
    
    try:
        # 修改配置文件中的标的
        with open("run_t0_backtest.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换标的代码
        old_symbol = "'SH600036'"
        new_symbol = f"'{symbol}'"
        
        if old_symbol in content:
            new_content = content.replace(old_symbol, new_symbol)
            
            with open("run_t0_backtest.py", 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 已更新配置文件，目标标的: {symbol}")
            
            # 运行回测
            print(f"🚀 开始运行 {symbol} 的T0策略回测...")
            
            # 这里可以调用回测函数
            # 由于run_improved_t0_backtest是一个完整的脚本，我们通过subprocess调用
            import subprocess
            result = subprocess.run([
                sys.executable, "run_t0_backtest.py"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ {symbol} 回测完成")
                print("回测输出:")
                print(result.stdout[-500:])  # 显示最后500字符
                return True
            else:
                print(f"❌ {symbol} 回测失败")
                print("错误信息:")
                print(result.stderr[-500:])
                return False
                
        else:
            print(f"❌ 未找到配置中的标的代码")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
    
    finally:
        # 恢复原始配置文件
        if os.path.exists(backup_file):
            shutil.move(backup_file, "run_t0_backtest.py")
            print("✅ 已恢复原始配置文件")

def main():
    """主函数"""
    print("🧪 T0策略多标的回测测试")
    print("="*80)
    
    # 获取可用的标的列表
    data_dir = "data/fixed_processed"
    available_symbols = []
    
    if os.path.exists(data_dir):
        for file in os.listdir(data_dir):
            if file.endswith('.csv') and file.startswith('SH') and file != 'SH000001.csv':
                symbol = file.replace('.csv', '')
                available_symbols.append(symbol)
    
    available_symbols = sorted(available_symbols)
    
    print(f"📊 发现 {len(available_symbols)} 个可用标的:")
    for i, symbol in enumerate(available_symbols, 1):
        print(f"  {i}. {symbol}")
    
    # 选择要测试的标的
    print(f"\n请选择要测试的标的 (1-{len(available_symbols)}, 或输入 'all' 测试所有):")
    choice = input("输入选择: ").strip()
    
    test_symbols = []
    
    if choice.lower() == 'all':
        test_symbols = available_symbols
        print(f"🎯 将测试所有 {len(test_symbols)} 个标的")
    else:
        try:
            index = int(choice) - 1
            if 0 <= index < len(available_symbols):
                test_symbols = [available_symbols[index]]
                print(f"🎯 将测试: {test_symbols[0]}")
            else:
                print("❌ 无效的选择")
                return
        except ValueError:
            print("❌ 无效的输入")
            return
    
    # 执行测试
    success_count = 0
    total_count = len(test_symbols)
    
    for symbol in test_symbols:
        success = test_symbol_backtest(symbol)
        if success:
            success_count += 1
    
    # 测试结果汇总
    print(f"\n{'='*80}")
    print("🏆 测试结果汇总")
    print(f"{'='*80}")
    print(f"总测试数量: {total_count}")
    print(f"成功数量: {success_count}")
    print(f"失败数量: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count > 0:
        print(f"\n✅ 测试完成！现在可以在Streamlit应用中查看不同标的的回测结果")
        print(f"💡 提示: 在Streamlit侧边栏选择不同的标的进行分析")

if __name__ == "__main__":
    main()
