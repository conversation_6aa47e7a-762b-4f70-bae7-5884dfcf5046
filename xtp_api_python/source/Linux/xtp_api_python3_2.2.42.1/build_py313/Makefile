# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named vnxtpquote

# Build rule for target.
vnxtpquote: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 vnxtpquote
.PHONY : vnxtpquote

# fast build rule for target.
vnxtpquote/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtpquote.dir/build.make CMakeFiles/vnxtpquote.dir/build
.PHONY : vnxtpquote/fast

#=============================================================================
# Target rules for targets named vnxtptrader

# Build rule for target.
vnxtptrader: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 vnxtptrader
.PHONY : vnxtptrader

# fast build rule for target.
vnxtptrader/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtptrader.dir/build.make CMakeFiles/vnxtptrader.dir/build
.PHONY : vnxtptrader/fast

vnxtpquote/vnxtpquote.o: vnxtpquote/vnxtpquote.cpp.o
.PHONY : vnxtpquote/vnxtpquote.o

# target to build an object file
vnxtpquote/vnxtpquote.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtpquote.dir/build.make CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.o
.PHONY : vnxtpquote/vnxtpquote.cpp.o

vnxtpquote/vnxtpquote.i: vnxtpquote/vnxtpquote.cpp.i
.PHONY : vnxtpquote/vnxtpquote.i

# target to preprocess a source file
vnxtpquote/vnxtpquote.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtpquote.dir/build.make CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.i
.PHONY : vnxtpquote/vnxtpquote.cpp.i

vnxtpquote/vnxtpquote.s: vnxtpquote/vnxtpquote.cpp.s
.PHONY : vnxtpquote/vnxtpquote.s

# target to generate assembly for a file
vnxtpquote/vnxtpquote.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtpquote.dir/build.make CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.s
.PHONY : vnxtpquote/vnxtpquote.cpp.s

vnxtptrader/vnxtptrader.o: vnxtptrader/vnxtptrader.cpp.o
.PHONY : vnxtptrader/vnxtptrader.o

# target to build an object file
vnxtptrader/vnxtptrader.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtptrader.dir/build.make CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.o
.PHONY : vnxtptrader/vnxtptrader.cpp.o

vnxtptrader/vnxtptrader.i: vnxtptrader/vnxtptrader.cpp.i
.PHONY : vnxtptrader/vnxtptrader.i

# target to preprocess a source file
vnxtptrader/vnxtptrader.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtptrader.dir/build.make CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.i
.PHONY : vnxtptrader/vnxtptrader.cpp.i

vnxtptrader/vnxtptrader.s: vnxtptrader/vnxtptrader.cpp.s
.PHONY : vnxtptrader/vnxtptrader.s

# target to generate assembly for a file
vnxtptrader/vnxtptrader.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtptrader.dir/build.make CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.s
.PHONY : vnxtptrader/vnxtptrader.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... vnxtpquote"
	@echo "... vnxtptrader"
	@echo "... vnxtpquote/vnxtpquote.o"
	@echo "... vnxtpquote/vnxtpquote.i"
	@echo "... vnxtpquote/vnxtpquote.s"
	@echo "... vnxtptrader/vnxtptrader.o"
	@echo "... vnxtptrader/vnxtptrader.i"
	@echo "... vnxtptrader/vnxtptrader.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

