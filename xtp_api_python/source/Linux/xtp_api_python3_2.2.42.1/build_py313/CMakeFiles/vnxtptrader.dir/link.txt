/usr/bin/c++ -fPIC  -fPIC -std=c++11 -O3 -DNDEBUG -Wl,--dependency-file=CMakeFiles/vnxtptrader.dir/link.d -shared -Wl,-soname,vnxtptrader.so -o lib/vnxtptrader.so CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.o  -Wl,-rpath,/home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/xtpapi /usr/lib/libboost_python313.so.1.87.0 /usr/lib/libboost_thread.so.1.87.0 /usr/lib/libboost_date_time.so.1.87.0 /usr/lib/libboost_chrono.so.1.87.0 /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/xtpapi/libxtptraderapi.so /usr/lib/libboost_container.so.1.87.0 /usr/lib/libboost_graph.so.1.87.0 /usr/lib/libboost_system.so.1.87.0
