# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313

# Include any dependencies generated for this target.
include CMakeFiles/vnxtptrader.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/vnxtptrader.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/vnxtptrader.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/vnxtptrader.dir/flags.make

CMakeFiles/vnxtptrader.dir/codegen:
.PHONY : CMakeFiles/vnxtptrader.dir/codegen

CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.o: CMakeFiles/vnxtptrader.dir/flags.make
CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.o: /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/vnxtptrader/vnxtptrader.cpp
CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.o: CMakeFiles/vnxtptrader.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.o -MF CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.o.d -o CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.o -c /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/vnxtptrader/vnxtptrader.cpp

CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/vnxtptrader/vnxtptrader.cpp > CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.i

CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/vnxtptrader/vnxtptrader.cpp -o CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.s

# Object files for target vnxtptrader
vnxtptrader_OBJECTS = \
"CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.o"

# External object files for target vnxtptrader
vnxtptrader_EXTERNAL_OBJECTS =

lib/vnxtptrader.so: CMakeFiles/vnxtptrader.dir/vnxtptrader/vnxtptrader.cpp.o
lib/vnxtptrader.so: CMakeFiles/vnxtptrader.dir/build.make
lib/vnxtptrader.so: CMakeFiles/vnxtptrader.dir/compiler_depend.ts
lib/vnxtptrader.so: /usr/lib/libboost_python313.so.1.87.0
lib/vnxtptrader.so: /usr/lib/libboost_thread.so.1.87.0
lib/vnxtptrader.so: /usr/lib/libboost_date_time.so.1.87.0
lib/vnxtptrader.so: /usr/lib/libboost_chrono.so.1.87.0
lib/vnxtptrader.so: /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/xtpapi/libxtptraderapi.so
lib/vnxtptrader.so: /usr/lib/libboost_container.so.1.87.0
lib/vnxtptrader.so: /usr/lib/libboost_graph.so.1.87.0
lib/vnxtptrader.so: /usr/lib/libboost_system.so.1.87.0
lib/vnxtptrader.so: CMakeFiles/vnxtptrader.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX shared library lib/vnxtptrader.so"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/vnxtptrader.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/vnxtptrader.dir/build: lib/vnxtptrader.so
.PHONY : CMakeFiles/vnxtptrader.dir/build

CMakeFiles/vnxtptrader.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/vnxtptrader.dir/cmake_clean.cmake
.PHONY : CMakeFiles/vnxtptrader.dir/clean

CMakeFiles/vnxtptrader.dir/depend:
	cd /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313 && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1 /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1 /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313 /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313 /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles/vnxtptrader.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/vnxtptrader.dir/depend

