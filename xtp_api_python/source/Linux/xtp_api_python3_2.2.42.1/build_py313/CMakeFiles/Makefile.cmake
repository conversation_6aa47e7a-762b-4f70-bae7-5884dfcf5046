# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/CMakeLists.txt"
  "CMakeFiles/3.31.6/CMakeCCompiler.cmake"
  "CMakeFiles/3.31.6/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.31.6/CMakeSystem.cmake"
  "/usr/lib/cmake/Boost-1.87.0/BoostConfig.cmake"
  "/usr/lib/cmake/Boost-1.87.0/BoostConfigVersion.cmake"
  "/usr/lib/cmake/BoostDetectToolset-1.87.0.cmake"
  "/usr/lib/cmake/boost_chrono-1.87.0/boost_chrono-config-version.cmake"
  "/usr/lib/cmake/boost_chrono-1.87.0/boost_chrono-config.cmake"
  "/usr/lib/cmake/boost_chrono-1.87.0/libboost_chrono-variant-shared.cmake"
  "/usr/lib/cmake/boost_chrono-1.87.0/libboost_chrono-variant-static.cmake"
  "/usr/lib/cmake/boost_container-1.87.0/boost_container-config-version.cmake"
  "/usr/lib/cmake/boost_container-1.87.0/boost_container-config.cmake"
  "/usr/lib/cmake/boost_container-1.87.0/libboost_container-variant-shared.cmake"
  "/usr/lib/cmake/boost_container-1.87.0/libboost_container-variant-static.cmake"
  "/usr/lib/cmake/boost_date_time-1.87.0/boost_date_time-config-version.cmake"
  "/usr/lib/cmake/boost_date_time-1.87.0/boost_date_time-config.cmake"
  "/usr/lib/cmake/boost_date_time-1.87.0/libboost_date_time-variant-shared.cmake"
  "/usr/lib/cmake/boost_date_time-1.87.0/libboost_date_time-variant-static.cmake"
  "/usr/lib/cmake/boost_graph-1.87.0/boost_graph-config-version.cmake"
  "/usr/lib/cmake/boost_graph-1.87.0/boost_graph-config.cmake"
  "/usr/lib/cmake/boost_graph-1.87.0/libboost_graph-variant-shared.cmake"
  "/usr/lib/cmake/boost_graph-1.87.0/libboost_graph-variant-static.cmake"
  "/usr/lib/cmake/boost_headers-1.87.0/boost_headers-config-version.cmake"
  "/usr/lib/cmake/boost_headers-1.87.0/boost_headers-config.cmake"
  "/usr/lib/cmake/boost_python-1.87.0/boost_python-config-version.cmake"
  "/usr/lib/cmake/boost_python-1.87.0/boost_python-config.cmake"
  "/usr/lib/cmake/boost_python-1.87.0/libboost_python-variant-shared-py3.13.cmake"
  "/usr/lib/cmake/boost_python-1.87.0/libboost_python-variant-static-py3.13.cmake"
  "/usr/lib/cmake/boost_system-1.87.0/boost_system-config-version.cmake"
  "/usr/lib/cmake/boost_system-1.87.0/boost_system-config.cmake"
  "/usr/lib/cmake/boost_system-1.87.0/libboost_system-variant-shared.cmake"
  "/usr/lib/cmake/boost_system-1.87.0/libboost_system-variant-static.cmake"
  "/usr/lib/cmake/boost_thread-1.87.0/boost_thread-config-version.cmake"
  "/usr/lib/cmake/boost_thread-1.87.0/boost_thread-config.cmake"
  "/usr/lib/cmake/boost_thread-1.87.0/libboost_thread-variant-shared.cmake"
  "/usr/lib/cmake/boost_thread-1.87.0/libboost_thread-variant-static.cmake"
  "/usr/share/cmake/Modules/CMakeCCompiler.cmake.in"
  "/usr/share/cmake/Modules/CMakeCCompilerABI.c"
  "/usr/share/cmake/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/share/cmake/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/share/cmake/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake"
  "/usr/share/cmake/Modules/CMakeDetermineSystem.cmake"
  "/usr/share/cmake/Modules/CMakeFindBinUtils.cmake"
  "/usr/share/cmake/Modules/CMakeFindDependencyMacro.cmake"
  "/usr/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake"
  "/usr/share/cmake/Modules/CMakeSystem.cmake.in"
  "/usr/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake/Modules/CMakeTestCCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/share/cmake/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/share/cmake/Modules/CMakeUnixFindMake.cmake"
  "/usr/share/cmake/Modules/CheckCSourceCompiles.cmake"
  "/usr/share/cmake/Modules/CheckIncludeFile.cmake"
  "/usr/share/cmake/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/share/cmake/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/share/cmake/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/share/cmake/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/LCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake/Modules/FindBoost.cmake"
  "/usr/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake/Modules/FindThreads.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "/usr/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake"
  "/usr/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/share/cmake/Modules/Internal/FeatureTesting.cmake"
  "/usr/share/cmake/Modules/Linker/GNU-C.cmake"
  "/usr/share/cmake/Modules/Linker/GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Linker/GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU-C.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-Determine-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake/Modules/Platform/Linux-Initialize.cmake"
  "/usr/share/cmake/Modules/Platform/Linux.cmake"
  "/usr/share/cmake/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.31.6/CMakeSystem.cmake"
  "CMakeFiles/3.31.6/CMakeCCompiler.cmake"
  "CMakeFiles/3.31.6/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.31.6/CMakeCCompiler.cmake"
  "CMakeFiles/3.31.6/CMakeCXXCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/vnxtpquote.dir/DependInfo.cmake"
  "CMakeFiles/vnxtptrader.dir/DependInfo.cmake"
  )
