# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313

# Include any dependencies generated for this target.
include CMakeFiles/vnxtpquote.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/vnxtpquote.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/vnxtpquote.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/vnxtpquote.dir/flags.make

CMakeFiles/vnxtpquote.dir/codegen:
.PHONY : CMakeFiles/vnxtpquote.dir/codegen

CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.o: CMakeFiles/vnxtpquote.dir/flags.make
CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.o: /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/vnxtpquote/vnxtpquote.cpp
CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.o: CMakeFiles/vnxtpquote.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.o -MF CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.o.d -o CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.o -c /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/vnxtpquote/vnxtpquote.cpp

CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/vnxtpquote/vnxtpquote.cpp > CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.i

CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/vnxtpquote/vnxtpquote.cpp -o CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.s

# Object files for target vnxtpquote
vnxtpquote_OBJECTS = \
"CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.o"

# External object files for target vnxtpquote
vnxtpquote_EXTERNAL_OBJECTS =

lib/vnxtpquote.so: CMakeFiles/vnxtpquote.dir/vnxtpquote/vnxtpquote.cpp.o
lib/vnxtpquote.so: CMakeFiles/vnxtpquote.dir/build.make
lib/vnxtpquote.so: CMakeFiles/vnxtpquote.dir/compiler_depend.ts
lib/vnxtpquote.so: /usr/lib/libboost_python313.so.1.87.0
lib/vnxtpquote.so: /usr/lib/libboost_thread.so.1.87.0
lib/vnxtpquote.so: /usr/lib/libboost_date_time.so.1.87.0
lib/vnxtpquote.so: /usr/lib/libboost_chrono.so.1.87.0
lib/vnxtpquote.so: /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/xtpapi/libxtpquoteapi.so
lib/vnxtpquote.so: /usr/lib/libboost_container.so.1.87.0
lib/vnxtpquote.so: /usr/lib/libboost_graph.so.1.87.0
lib/vnxtpquote.so: /usr/lib/libboost_system.so.1.87.0
lib/vnxtpquote.so: CMakeFiles/vnxtpquote.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX shared library lib/vnxtpquote.so"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/vnxtpquote.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/vnxtpquote.dir/build: lib/vnxtpquote.so
.PHONY : CMakeFiles/vnxtpquote.dir/build

CMakeFiles/vnxtpquote.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/vnxtpquote.dir/cmake_clean.cmake
.PHONY : CMakeFiles/vnxtpquote.dir/clean

CMakeFiles/vnxtpquote.dir/depend:
	cd /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313 && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1 /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1 /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313 /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313 /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles/vnxtpquote.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/vnxtpquote.dir/depend

