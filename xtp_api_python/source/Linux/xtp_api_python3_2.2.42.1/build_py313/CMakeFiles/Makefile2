# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/vnxtpquote.dir/all
all: CMakeFiles/vnxtptrader.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/vnxtpquote.dir/codegen
codegen: CMakeFiles/vnxtptrader.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/vnxtpquote.dir/clean
clean: CMakeFiles/vnxtptrader.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/vnxtpquote.dir

# All Build rule for target.
CMakeFiles/vnxtpquote.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtpquote.dir/build.make CMakeFiles/vnxtpquote.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtpquote.dir/build.make CMakeFiles/vnxtpquote.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles --progress-num=1,2 "Built target vnxtpquote"
.PHONY : CMakeFiles/vnxtpquote.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/vnxtpquote.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/vnxtpquote.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles 0
.PHONY : CMakeFiles/vnxtpquote.dir/rule

# Convenience name for target.
vnxtpquote: CMakeFiles/vnxtpquote.dir/rule
.PHONY : vnxtpquote

# codegen rule for target.
CMakeFiles/vnxtpquote.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtpquote.dir/build.make CMakeFiles/vnxtpquote.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles --progress-num=1,2 "Finished codegen for target vnxtpquote"
.PHONY : CMakeFiles/vnxtpquote.dir/codegen

# clean rule for target.
CMakeFiles/vnxtpquote.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtpquote.dir/build.make CMakeFiles/vnxtpquote.dir/clean
.PHONY : CMakeFiles/vnxtpquote.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/vnxtptrader.dir

# All Build rule for target.
CMakeFiles/vnxtptrader.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtptrader.dir/build.make CMakeFiles/vnxtptrader.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtptrader.dir/build.make CMakeFiles/vnxtptrader.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles --progress-num=3,4 "Built target vnxtptrader"
.PHONY : CMakeFiles/vnxtptrader.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/vnxtptrader.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/vnxtptrader.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles 0
.PHONY : CMakeFiles/vnxtptrader.dir/rule

# Convenience name for target.
vnxtptrader: CMakeFiles/vnxtptrader.dir/rule
.PHONY : vnxtptrader

# codegen rule for target.
CMakeFiles/vnxtptrader.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtptrader.dir/build.make CMakeFiles/vnxtptrader.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Documents/augment-projects/T0T/xtp_api_python/source/Linux/xtp_api_python3_2.2.42.1/build_py313/CMakeFiles --progress-num=3,4 "Finished codegen for target vnxtptrader"
.PHONY : CMakeFiles/vnxtptrader.dir/codegen

# clean rule for target.
CMakeFiles/vnxtptrader.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/vnxtptrader.dir/build.make CMakeFiles/vnxtptrader.dir/clean
.PHONY : CMakeFiles/vnxtptrader.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

