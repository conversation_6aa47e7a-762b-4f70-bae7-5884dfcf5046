#!/bin/bash

# 创建构建目录
mkdir -p build_py313
cd build_py313

# 复制XTP API库文件到当前目录
cp -f ../xtpapi/libxtpquoteapi.so ./
cp -f ../xtpapi/libxtptraderapi.so ./

# 运行CMake
cmake -DCMAKE_BUILD_TYPE=Release ..

# 编译
make

# 创建输出目录
mkdir -p ../../../../../lib/
mkdir -p ../../../../../bin/Linux/py313/

# 复制编译好的库文件到输出目录
cp -f lib/vnxtpquote.so ../../../../../lib/
cp -f lib/vnxtptrader.so ../../../../../lib/
cp -f lib/vnxtpquote.so ../../../../../bin/Linux/py313/
cp -f lib/vnxtptrader.so ../../../../../bin/Linux/py313/
cp -f libxtpquoteapi.so ../../../../../lib/
cp -f libxtptraderapi.so ../../../../../lib/
cp -f libxtpquoteapi.so ../../../../../bin/Linux/py313/
cp -f libxtptraderapi.so ../../../../../bin/Linux/py313/

echo "Build completed for Python 3.13"
