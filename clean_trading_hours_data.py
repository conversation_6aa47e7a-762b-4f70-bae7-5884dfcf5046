#!/usr/bin/env python3
"""
清理交易时间数据 - 移除9:30之前和15:00之后的数据
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, time

def clean_trading_hours(df):
    """
    清理交易时间数据，只保留正常交易时间的数据
    
    Args:
        df: DataFrame，包含datetime索引的分钟级数据
        
    Returns:
        DataFrame: 清理后的数据
    """
    # 确保索引是datetime类型
    if not isinstance(df.index, pd.DatetimeIndex):
        df.index = pd.to_datetime(df.index)
    
    # 定义正常交易时间
    morning_start = time(9, 30)  # 9:30
    morning_end = time(11, 30)   # 11:30
    afternoon_start = time(13, 0) # 13:00
    afternoon_end = time(15, 0)   # 15:00
    
    # 过滤交易时间
    mask = (
        # 上午交易时间：9:30-11:30
        ((df.index.time >= morning_start) & (df.index.time <= morning_end)) |
        # 下午交易时间：13:00-15:00
        ((df.index.time >= afternoon_start) & (df.index.time <= afternoon_end))
    )
    
    cleaned_df = df[mask].copy()
    
    return cleaned_df

def process_single_file(file_path):
    """
    处理单个CSV文件
    
    Args:
        file_path: str，文件路径
        
    Returns:
        tuple: (原始行数, 清理后行数, 是否成功)
    """
    try:
        print(f"处理文件: {file_path}")
        
        # 读取数据
        df = pd.read_csv(file_path)
        original_count = len(df)
        
        # 确保有datetime列
        if 'datetime' not in df.columns:
            print(f"  ❌ 文件缺少datetime列: {file_path}")
            return original_count, 0, False
        
        # 设置datetime为索引
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        # 清理交易时间
        cleaned_df = clean_trading_hours(df)
        cleaned_count = len(cleaned_df)
        
        # 重置索引，保存datetime列
        cleaned_df.reset_index(inplace=True)
        
        # 保存清理后的数据
        cleaned_df.to_csv(file_path, index=False)
        
        print(f"  ✅ 原始数据: {original_count:,} 行")
        print(f"  ✅ 清理后: {cleaned_count:,} 行")
        print(f"  ✅ 移除: {original_count - cleaned_count:,} 行 ({(original_count - cleaned_count)/original_count*100:.1f}%)")
        
        return original_count, cleaned_count, True
        
    except Exception as e:
        print(f"  ❌ 处理失败: {e}")
        return 0, 0, False

def analyze_time_distribution(file_path):
    """
    分析时间分布
    
    Args:
        file_path: str，文件路径
    """
    try:
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        # 按小时统计
        df['hour'] = df['datetime'].dt.hour
        hour_counts = df['hour'].value_counts().sort_index()
        
        print(f"  📊 时间分布:")
        for hour, count in hour_counts.items():
            print(f"    {hour:02d}:xx - {count:,} 条记录")
            
        # 检查异常时间
        abnormal_times = df[
            ~(
                # 正常交易时间
                ((df['datetime'].dt.time >= time(9, 30)) & (df['datetime'].dt.time <= time(11, 30))) |
                ((df['datetime'].dt.time >= time(13, 0)) & (df['datetime'].dt.time <= time(15, 0)))
            )
        ]
        
        if len(abnormal_times) > 0:
            print(f"  ⚠️ 发现 {len(abnormal_times):,} 条异常时间数据")
            print(f"    最早: {abnormal_times['datetime'].min()}")
            print(f"    最晚: {abnormal_times['datetime'].max()}")
        else:
            print(f"  ✅ 所有数据都在正常交易时间内")
            
    except Exception as e:
        print(f"  ❌ 分析失败: {e}")

def main():
    """主函数"""
    print("🧹 清理交易时间数据工具")
    print("="*80)
    
    # 数据目录
    data_dir = "data/fixed_processed"
    
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return
    
    # 获取所有CSV文件
    csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
    
    if not csv_files:
        print(f"❌ 在 {data_dir} 中未找到CSV文件")
        return
    
    print(f"📁 发现 {len(csv_files)} 个CSV文件")
    
    # 询问是否要先分析时间分布
    analyze_choice = input("\n是否要先分析时间分布? (y/n): ").strip().lower()
    
    if analyze_choice == 'y':
        print("\n📊 分析时间分布...")
        print("-" * 60)
        
        for file in sorted(csv_files)[:3]:  # 只分析前3个文件
            file_path = os.path.join(data_dir, file)
            print(f"\n📄 {file}:")
            analyze_time_distribution(file_path)
    
    # 询问是否要清理数据
    print("\n" + "="*80)
    clean_choice = input("是否要清理所有文件的交易时间数据? (y/n): ").strip().lower()
    
    if clean_choice != 'y':
        print("👋 操作取消")
        return
    
    # 处理所有文件
    print("\n🧹 开始清理数据...")
    print("-" * 60)
    
    total_original = 0
    total_cleaned = 0
    success_count = 0
    
    for file in sorted(csv_files):
        file_path = os.path.join(data_dir, file)
        original, cleaned, success = process_single_file(file_path)
        
        total_original += original
        total_cleaned += cleaned
        
        if success:
            success_count += 1
        
        print()  # 空行分隔
    
    # 汇总结果
    print("="*80)
    print("🏆 清理结果汇总")
    print("="*80)
    print(f"处理文件数: {len(csv_files)}")
    print(f"成功文件数: {success_count}")
    print(f"失败文件数: {len(csv_files) - success_count}")
    print(f"原始总数据: {total_original:,} 行")
    print(f"清理后总数据: {total_cleaned:,} 行")
    print(f"移除数据: {total_original - total_cleaned:,} 行")
    
    if total_original > 0:
        removal_rate = (total_original - total_cleaned) / total_original * 100
        print(f"移除比例: {removal_rate:.1f}%")
    
    print("\n✅ 数据清理完成!")
    print("💡 现在可以重新运行回测，数据将只包含正常交易时间")

if __name__ == "__main__":
    main()
