#!/usr/bin/env python3
"""
分析交易记录脚本
"""

import pandas as pd
import numpy as np

def analyze_trades():
    """分析交易记录"""
    # 读取最新的交易记录
    trades_df = pd.read_csv('results/trades_20250609_153541.csv')
    
    print("交易记录分析")
    print("=" * 60)
    
    # 基本统计
    print(f"总交易次数: {len(trades_df)}")
    print(f"买入交易: {len(trades_df[trades_df['type'] == 'buy'])}")
    print(f"卖出交易: {len(trades_df[trades_df['type'] == 'sell'])}")
    
    # 强制调整交易分析
    forced_trades = trades_df[trades_df['is_forced_adjustment'] == True]
    print(f"强制调整交易: {len(forced_trades)}")
    print(f"强制调整比例: {len(forced_trades)/len(trades_df):.2%}")
    
    if len(forced_trades) > 0:
        print("\n强制调整原因分布:")
        reason_counts = forced_trades['adjustment_reason'].value_counts()
        for reason, count in reason_counts.items():
            print(f"  {reason}: {count} ({count/len(forced_trades):.1%})")
    
    # 交易价值分析
    print(f"\n交易价值统计:")
    print(f"平均交易价值: {trades_df['value'].mean():.2f}")
    print(f"最大交易价值: {trades_df['value'].max():.2f}")
    print(f"最小交易价值: {trades_df['value'].min():.2f}")
    print(f"总交易价值: {trades_df['value'].sum():.2f}")
    
    # 持仓变化分析
    print(f"\n持仓变化:")
    print(f"最终持仓: {trades_df['holdings_after'].iloc[-1]:.3f}")
    print(f"最大持仓: {trades_df['holdings_after'].max():.3f}")
    print(f"最小持仓: {trades_df['holdings_after'].min():.3f}")
    
    # 现金变化分析
    print(f"\n现金变化:")
    initial_cash = 1000000  # 初始资金
    final_cash = trades_df['cash_after'].iloc[-1]
    print(f"初始现金: {initial_cash:.2f}")
    print(f"最终现金: {final_cash:.2f}")
    print(f"现金变化: {final_cash - initial_cash:.2f}")
    
    # 时间分析
    trades_df['time'] = pd.to_datetime(trades_df['time'])
    print(f"\n时间分析:")
    print(f"交易开始时间: {trades_df['time'].min()}")
    print(f"交易结束时间: {trades_df['time'].max()}")
    print(f"交易时间跨度: {trades_df['time'].max() - trades_df['time'].min()}")
    
    # 每日交易次数
    trades_df['date'] = trades_df['time'].dt.date
    daily_trades = trades_df.groupby('date').size()
    print(f"\n每日交易统计:")
    print(f"平均每日交易次数: {daily_trades.mean():.1f}")
    print(f"最大每日交易次数: {daily_trades.max()}")
    print(f"最小每日交易次数: {daily_trades.min()}")
    
    # 异常交易检测
    print(f"\n异常交易检测:")
    
    # 检查是否有同一时间的多笔交易
    same_time_trades = trades_df.groupby('time').size()
    multiple_trades = same_time_trades[same_time_trades > 1]
    if len(multiple_trades) > 0:
        print(f"同一时间多笔交易: {len(multiple_trades)} 个时间点")
        print(f"最多同时交易: {multiple_trades.max()} 笔")
    
    # 检查持仓是否合理
    unreasonable_holdings = trades_df[trades_df['holdings_after'] > 200]  # 假设200是不合理的上限
    if len(unreasonable_holdings) > 0:
        print(f"持仓异常记录: {len(unreasonable_holdings)}")
    
    # 检查价格异常
    price_changes = trades_df['price'].pct_change().abs()
    large_price_changes = price_changes[price_changes > 0.1]  # 10%以上的价格变化
    if len(large_price_changes) > 0:
        print(f"大幅价格变化: {len(large_price_changes)} 次")
    
    return trades_df

def analyze_performance():
    """分析性能表现"""
    # 读取最新的权益曲线
    equity_df = pd.read_csv('results/equity_curve_20250609_153541.csv')
    equity_df['date'] = pd.to_datetime(equity_df['date'])
    equity_df = equity_df.set_index('date')
    
    print("\n权益曲线分析")
    print("=" * 60)
    
    initial_equity = equity_df['equity'].iloc[0]
    final_equity = equity_df['equity'].iloc[-1]
    
    print(f"初始权益: {initial_equity:.2f}")
    print(f"最终权益: {final_equity:.2f}")
    print(f"总收益: {final_equity - initial_equity:.2f}")
    print(f"总收益率: {(final_equity / initial_equity - 1):.2%}")
    
    # 计算最大回撤
    equity_df['cummax'] = equity_df['equity'].cummax()
    equity_df['drawdown'] = (equity_df['cummax'] - equity_df['equity']) / equity_df['cummax']
    max_drawdown = equity_df['drawdown'].max()
    print(f"最大回撤: {max_drawdown:.2%}")
    
    # 计算日收益率
    equity_df['daily_return'] = equity_df['equity'].pct_change()
    
    # 胜率
    positive_days = len(equity_df[equity_df['daily_return'] > 0])
    total_days = len(equity_df) - 1  # 减去第一天（NaN）
    win_rate = positive_days / total_days if total_days > 0 else 0
    print(f"日胜率: {win_rate:.2%}")
    
    # 最大单日亏损
    max_daily_loss = equity_df['daily_return'].min()
    print(f"最大单日亏损: {max_daily_loss:.2%}")
    
    # 最大单日收益
    max_daily_gain = equity_df['daily_return'].max()
    print(f"最大单日收益: {max_daily_gain:.2%}")

def main():
    """主函数"""
    try:
        trades_df = analyze_trades()
        analyze_performance()
        
        # 保存分析结果
        print(f"\n保存前10笔交易记录:")
        print(trades_df.head(10)[['time', 'type', 'price', 'value', 'holdings_after', 'is_forced_adjustment', 'adjustment_reason']])
        
    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
