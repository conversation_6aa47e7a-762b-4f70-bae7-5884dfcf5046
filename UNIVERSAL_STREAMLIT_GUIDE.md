# 🚀 通用T0交易系统Streamlit应用使用指南

## 📋 概述

现在的Streamlit应用已经升级为**通用版本**，支持分析任意标的的T0策略回测结果，不再局限于招商银行。

## ✨ 新功能特性

### 🎯 **动态标的选择**
- **侧边栏选择器**: 自动检测所有可用的股票数据
- **智能识别**: 自动识别最新的回测结果对应的标的
- **实时切换**: 无需重启应用即可切换分析不同标的

### 📊 **自适应显示**
- **动态标题**: 根据选择的标的自动更新页面标题
- **智能标签**: 图表标签和Y轴标题自动适配股票名称
- **数据信息**: 侧边栏显示当前分析标的的详细信息

### 🔄 **完整兼容性**
- **向后兼容**: 完全兼容现有的回测结果文件
- **多标的支持**: 支持所有已处理的10支股票
- **自动检测**: 自动检测可用的数据文件

## 🎮 使用方法

### 1. **启动应用**
```bash
cd /home/<USER>/Documents/augment-projects/T0T
streamlit run streamlit_app.py
```

### 2. **选择分析标的**
- 在左侧边栏的"📊 分析设置"中
- 使用"选择分析标的"下拉菜单
- 选择要分析的股票（显示格式：股票名称 (代码)）

### 3. **查看分析结果**
- **第1行**: 目标股票价格走势 + 交易信号
- **第2行**: 上证指数走势（市场基准）
- **第3行**: 收益率对比（股票 vs 指数 vs T0策略）
- **第4行**: 持仓变化
- **第5行**: 现金流变化

## 📈 支持的标的列表

当前系统支持以下10支股票的分析：

| 序号 | 股票代码 | 股票名称 | 数据状态 |
|------|----------|----------|----------|
| 1 | SH600036 | 招商银行 | ✅ 已回测 |
| 2 | SH600000 | 浦发银行 | ✅ 数据就绪 |
| 3 | SH600519 | 贵州茅台 | ✅ 数据就绪 |
| 4 | SH600030 | 中信证券 | ✅ 数据就绪 |
| 5 | SH600887 | 伊利股份 | ✅ 数据就绪 |
| 6 | SH600276 | 恒瑞医药 | ✅ 数据就绪 |
| 7 | SH600585 | 海螺水泥 | ✅ 数据就绪 |
| 8 | SH600104 | 上汽集团 | ✅ 数据就绪 |
| 9 | SH600050 | 中国联通 | ✅ 数据就绪 |
| 10 | SH000001 | 上证指数 | ✅ 基准数据 |

## 🔧 回测其他标的

### 方法1: 使用通用回测脚本
```bash
python run_backtest_any_symbol.py
```
- 交互式选择要回测的标的
- 自动运行T0策略回测
- 生成结果文件供Streamlit分析

### 方法2: 修改原始回测脚本
```bash
# 编辑 run_t0_backtest.py
# 修改第55行的symbols配置
'symbols': ['SH600519']  # 改为要回测的标的
```

## 📊 界面功能详解

### 🎛️ **侧边栏控制**
- **分析设置**
  - 标的选择下拉菜单
  - 数据信息显示
- **分析控制**
  - 日期范围选择器
  - 默认显示一周数据

### 📈 **主要图表**
1. **价格走势图**
   - K线图 + 移动平均线
   - 买卖信号标记
   - 悬停显示详细信息

2. **基准对比图**
   - 上证指数独立显示
   - 清晰的市场走势参考

3. **收益率对比**
   - 归一化收益率曲线
   - 三线对比（股票/指数/策略）

4. **仓位监控**
   - 持仓数量变化
   - 现金流变化

### 📊 **性能指标**
- **核心指标**: 总收益率、最大回撤、夏普比率、日胜率
- **交易统计**: 交易次数、T0占比、成本分析
- **详细数据**: 交易记录表格

## 🎯 使用场景

### 1. **单标的深度分析**
- 选择特定股票进行详细分析
- 查看T0策略在该股票上的表现
- 对比股票表现与市场基准

### 2. **多标的对比分析**
- 切换不同标的查看回测结果
- 比较T0策略在不同股票上的效果
- 识别最适合T0策略的标的

### 3. **策略优化研究**
- 分析不同股票的交易信号质量
- 研究持仓和现金流管理效果
- 优化策略参数设置

## 🔄 工作流程

### 完整分析流程
1. **数据准备**: 确保目标标的数据已处理
2. **运行回测**: 使用通用回测脚本
3. **启动应用**: 运行Streamlit应用
4. **选择标的**: 在侧边栏选择要分析的股票
5. **深度分析**: 查看各项指标和图表
6. **对比研究**: 切换不同标的进行对比

### 新标的添加流程
1. **数据处理**: 使用`fix_data_processing.py`处理新数据
2. **回测执行**: 使用`run_backtest_any_symbol.py`运行回测
3. **应用分析**: 在Streamlit中选择新标的进行分析

## 🎉 优势特性

### ✅ **用户友好**
- 无需修改代码即可切换标的
- 直观的下拉菜单选择
- 清晰的数据信息显示

### ✅ **功能完整**
- 支持所有已处理的股票数据
- 完整的T0策略分析功能
- 丰富的可视化图表

### ✅ **扩展性强**
- 易于添加新的股票标的
- 支持自定义分析时间范围
- 可扩展的性能指标

### ✅ **专业级分析**
- 多维度数据展示
- 专业的金融图表
- 详细的交易记录

## 💡 使用建议

1. **首次使用**: 建议先选择招商银行（已有完整回测结果）
2. **对比分析**: 可以依次选择不同标的进行对比
3. **时间范围**: 根据分析需要调整日期范围
4. **深度研究**: 结合交易记录表格进行详细分析

现在您拥有了一个真正通用的、专业级的T0交易策略分析平台！🚀
