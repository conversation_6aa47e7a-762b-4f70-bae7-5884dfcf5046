#!/usr/bin/env python3
"""
处理原始数据包含2025年数据 - 重新处理/home/<USER>/Work/LLM/data/extracted/目录
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, time
import glob

def fix_csv_format(content):
    """
    修复CSV格式问题 - 处理合并的列
    """
    lines = content.strip().split('\n')
    fixed_lines = []
    
    for line in lines:
        # 检查是否是标题行
        if 'datetime' in line.lower():
            fixed_lines.append(line)
            continue
        
        # 尝试分割数据
        parts = line.split(',')
        
        # 如果列数不对，尝试修复
        if len(parts) < 6:
            # 可能存在合并的数据，尝试智能分割
            # 查找数字模式
            import re
            
            # 查找所有数字（包括小数）
            numbers = re.findall(r'\d+\.?\d*', line)
            
            if len(numbers) >= 6:
                # 重新构造行：datetime,open,high,low,close,volume,amount
                # 假设第一个是时间戳相关，后面6个是OHLCVA数据
                if len(numbers) >= 7:
                    # 取后6个数字作为OHLCVA数据
                    ohlcva = numbers[-6:]
                    # 时间戳从原始行中提取
                    datetime_part = line.split(',')[0] if ',' in line else line.split()[0]
                    fixed_line = f"{datetime_part},{','.join(ohlcva)}"
                    fixed_lines.append(fixed_line)
                else:
                    fixed_lines.append(line)
            else:
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def clean_trading_hours(df):
    """
    清理交易时间数据，只保留正常交易时间的数据
    """
    # 确保索引是datetime类型
    if not isinstance(df.index, pd.DatetimeIndex):
        df.index = pd.to_datetime(df.index)
    
    # 定义正常交易时间
    morning_start = time(9, 30)  # 9:30
    morning_end = time(11, 30)   # 11:30
    afternoon_start = time(13, 0) # 13:00
    afternoon_end = time(15, 0)   # 15:00
    
    # 过滤交易时间
    mask = (
        # 上午交易时间：9:30-11:30
        ((df.index.time >= morning_start) & (df.index.time <= morning_end)) |
        # 下午交易时间：13:00-15:00
        ((df.index.time >= afternoon_start) & (df.index.time <= afternoon_end))
    )
    
    cleaned_df = df[mask].copy()
    return cleaned_df

def process_single_symbol(symbol, source_dir, output_dir):
    """
    处理单个股票的所有年份数据
    """
    print(f"\n📊 处理股票: {symbol}")
    
    all_data = []
    
    # 遍历所有年份目录
    for year in ['2023', '2024', '2025']:
        year_dir = os.path.join(source_dir, year)
        if not os.path.exists(year_dir):
            continue
        
        print(f"  📅 处理 {year} 年数据...")
        
        # 查找该股票的文件
        pattern = os.path.join(year_dir, '**', f'*{symbol}*.csv')
        files = glob.glob(pattern, recursive=True)
        
        for file_path in files:
            try:
                print(f"    📄 读取: {os.path.basename(file_path)}")
                
                # 读取原始文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 修复CSV格式
                fixed_content = fix_csv_format(content)
                
                # 解析为DataFrame
                from io import StringIO
                df = pd.read_csv(StringIO(fixed_content))
                
                # 确保有必要的列
                expected_columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
                if not all(col in df.columns for col in expected_columns[:6]):
                    print(f"    ⚠️ 列名不匹配，尝试重命名...")
                    # 尝试重命名列
                    if len(df.columns) >= 6:
                        df.columns = ['datetime', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                
                # 转换datetime列
                df['datetime'] = pd.to_datetime(df['datetime'])
                
                # 设置索引
                df.set_index('datetime', inplace=True)
                
                # 清理交易时间
                df = clean_trading_hours(df)
                
                if not df.empty:
                    all_data.append(df)
                    print(f"    ✅ 成功处理 {len(df)} 行数据")
                else:
                    print(f"    ⚠️ 清理后数据为空")
                    
            except Exception as e:
                print(f"    ❌ 处理失败: {e}")
                continue
    
    if not all_data:
        print(f"  ❌ {symbol} 没有有效数据")
        return False
    
    # 合并所有数据
    print(f"  🔗 合并 {len(all_data)} 个数据文件...")
    combined_df = pd.concat(all_data, axis=0)
    
    # 去重并排序
    combined_df = combined_df[~combined_df.index.duplicated(keep='first')]
    combined_df = combined_df.sort_index()
    
    # 数据验证
    print(f"  📊 数据统计:")
    print(f"    时间范围: {combined_df.index.min()} 到 {combined_df.index.max()}")
    print(f"    总数据量: {len(combined_df):,} 行")
    print(f"    数据完整性: {combined_df.isnull().sum().sum()} 个缺失值")
    
    # 保存到输出目录
    os.makedirs(output_dir, exist_ok=True)
    output_file = os.path.join(output_dir, f"{symbol}.csv")
    
    # 重置索引保存datetime列
    combined_df.reset_index(inplace=True)
    combined_df.to_csv(output_file, index=False)
    
    print(f"  ✅ 已保存到: {output_file}")
    return True

def main():
    """主函数"""
    print("🚀 处理原始数据包含2025年数据")
    print("="*80)
    
    # 数据路径
    source_dir = "/home/<USER>/Work/LLM/data/extracted"
    output_dir = "data/processed_with_2025"
    
    # 检查源目录
    if not os.path.exists(source_dir):
        print(f"❌ 源数据目录不存在: {source_dir}")
        return
    
    # 要处理的股票列表
    symbols = [
        'SH600000',  # 浦发银行
        'SH600030',  # 中信证券
        'SH600036',  # 招商银行
        'SH600050',  # 中国联通
        'SH600104',  # 上汽集团
        'SH600276',  # 恒瑞医药
        'SH600519',  # 贵州茅台
        'SH600585',  # 海螺水泥
        'SH600887',  # 伊利股份
        'SH000001'   # 上证指数
    ]
    
    print(f"📁 源数据目录: {source_dir}")
    print(f"📁 输出目录: {output_dir}")
    print(f"📊 待处理股票: {len(symbols)} 个")
    
    # 处理每个股票
    success_count = 0
    total_count = len(symbols)
    
    for i, symbol in enumerate(symbols, 1):
        print(f"\n{'='*60}")
        print(f"进度: {i}/{total_count} - {symbol}")
        print(f"{'='*60}")
        
        try:
            success = process_single_symbol(symbol, source_dir, output_dir)
            if success:
                success_count += 1
        except Exception as e:
            print(f"❌ 处理 {symbol} 时出错: {e}")
    
    # 汇总结果
    print(f"\n{'='*80}")
    print("🏆 处理结果汇总")
    print(f"{'='*80}")
    print(f"总股票数: {total_count}")
    print(f"成功处理: {success_count}")
    print(f"失败数量: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count > 0:
        print(f"\n✅ 数据处理完成！")
        print(f"📁 输出目录: {output_dir}")
        print(f"💡 现在可以使用新数据进行回测")
    else:
        print(f"\n❌ 没有成功处理任何数据")

if __name__ == "__main__":
    main()
