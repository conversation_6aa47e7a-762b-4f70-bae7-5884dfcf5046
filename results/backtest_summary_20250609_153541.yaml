backtest_config:
  end_date: '2024-12-31'
  initial_capital: 1000000
  start_date: '2023-01-01'
  symbols:
  - SH000001
  - SH600000
  - SZ000001
  - SZ000002
  - SH600028
  - SH600036
  - SH600519
  - SH601318
  - SH601398
  - SZ002594
performance_metrics:
  annual_return: !!python/object/apply:numpy._core.multiarray.scalar
  - &id001 !!python/object/apply:numpy.dtype
    args:
    - f8
    - false
    - true
    state: !!python/tuple
    - 3
    - <
    - null
    - null
    - null
    - -1
    - -1
    - 0
  - !!binary |
    UCaqXt4srr8=
  max_drawdown: !!python/object/apply:numpy._core.multiarray.scalar
  - *id001
  - !!binary |
    YA1ceeLxwT8=
  profit_loss_ratio: !!python/object/apply:numpy._core.multiarray.scalar
  - *id001
  - !!binary |
    6zzkIuZxQUA=
  sharpe_ratio: !!python/object/apply:numpy._core.multiarray.scalar
  - *id001
  - !!binary |
    R/s8q6MB5b8=
  total_return: !!python/object/apply:numpy._core.multiarray.scalar
  - *id001
  - !!binary |
    OOmBirQRwb8=
  total_trades: 458
  win_rate: 0.021834061135371178
timestamp: '2025-06-09T15:35:41.392883'
