backtest_config:
  end_date: '2024-12-31'
  initial_capital: 1000000
  start_date: '2023-01-01'
  symbols:
  - SH000001
  - SH600000
  - SZ000001
  - SZ000002
  - SH600028
  - SH600036
  - SH600519
  - SH601318
  - SH601398
  - SZ002594
performance_metrics:
  annual_return: !!python/object/apply:numpy._core.multiarray.scalar
  - &id001 !!python/object/apply:numpy.dtype
    args:
    - f8
    - false
    - true
    state: !!python/tuple
    - 3
    - <
    - null
    - null
    - null
    - -1
    - -1
    - 0
  - !!binary |
    0ORvQ/MGsr8=
  max_drawdown: !!python/object/apply:numpy._core.multiarray.scalar
  - *id001
  - !!binary |
    S2am7bR94T8=
  profit_loss_ratio: !!python/object/apply:numpy._core.multiarray.scalar
  - *id001
  - !!binary |
    hS8Bfp4POkA=
  sharpe_ratio: !!python/object/apply:numpy._core.multiarray.scalar
  - *id001
  - !!binary |
    CswUeR/+y78=
  total_return: !!python/object/apply:numpy._core.multiarray.scalar
  - *id001
  - !!binary |
    iKDyv2E7xL8=
  total_trades: 458
  win_rate: 0.04148471615720524
timestamp: '2025-06-09T15:32:57.625850'
